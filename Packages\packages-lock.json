{"dependencies": {"com.ivanmurzak.unity.mcp": {"version": "0.8.1", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.uielements": "1.0.0", "org.nuget.microsoft.bcl.memory": "9.0.4", "org.nuget.microsoft.aspnetcore.signalr.client": "9.0.4", "org.nuget.microsoft.aspnetcore.signalr.protocols.json": "9.0.4", "org.nuget.microsoft.codeanalysis.csharp": "4.13.0", "org.nuget.microsoft.extensions.caching.abstractions": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.hosting": "9.0.4", "org.nuget.microsoft.extensions.hosting.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.r3": "1.3.0", "org.nuget.system.text.json": "9.0.4"}, "url": "https://package.openupm.com"}, "com.rlabrecque.steamworks.net": {"version": "https://github.com/rlabrecque/Steamworks.NET.git?path=/com.rlabrecque.steamworks.net", "depth": 0, "source": "git", "dependencies": {}, "hash": "6b097c14dcf52cf139c79f8d882e5c87a4a6efb7"}, "com.unity.ai.navigation": {"version": "2.0.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.ai": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.21", "depth": 2, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.8.2", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.1", "depth": 2, "source": "registry", "dependencies": {"com.unity.burst": "1.8.17", "com.unity.test-framework": "1.4.5", "com.unity.nuget.mono-cecil": "1.11.4", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 2, "source": "builtin", "dependencies": {}}, "com.unity.feature.development": {"version": "1.0.2", "depth": 0, "source": "builtin", "dependencies": {"com.unity.ide.visualstudio": "2.0.23", "com.unity.ide.rider": "3.0.35", "com.unity.editorcoroutines": "1.0.0", "com.unity.performance.profile-analyzer": "1.2.3", "com.unity.test-framework": "1.5.1", "com.unity.testtools.codecoverage": "1.2.6"}}, "com.unity.ide.rider": {"version": "3.0.35", "depth": 1, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.23", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.multiplayer.center": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.uielements": "1.0.0"}}, "com.unity.nuget.mono-cecil": {"version": "1.11.4", "depth": 3, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.performance.profile-analyzer": {"version": "1.2.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.postprocessing": {"version": "3.4.0", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.physics": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.14", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.high-definition": {"version": "17.0.4", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.video": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.render-pipelines.core": "17.0.4", "com.unity.shadergraph": "17.0.4", "com.unity.visualeffectgraph": "17.0.4", "com.unity.render-pipelines.high-definition-config": "17.0.4"}}, "com.unity.render-pipelines.high-definition-config": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.4"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.searcher": {"version": "4.9.3", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.1.0", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.4", "com.unity.searcher": "4.9.3"}}, "com.unity.test-framework": {"version": "1.5.1", "depth": 1, "source": "builtin", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.test-framework.performance": {"version": "3.1.0", "depth": 3, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.33", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.testtools.codecoverage": {"version": "1.2.6", "depth": 1, "source": "registry", "dependencies": {"com.unity.test-framework": "1.0.16", "com.unity.settings-manager": "1.0.1"}, "url": "https://packages.unity.com"}, "com.unity.timeline": {"version": "1.8.7", "depth": 0, "source": "registry", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.visualeffectgraph": {"version": "17.0.4", "depth": 1, "source": "builtin", "dependencies": {"com.unity.shadergraph": "17.0.4", "com.unity.render-pipelines.core": "17.0.4"}}, "com.unity.visualscripting": {"version": "1.9.6", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "org.nuget.microsoft.aspnetcore.connections.abstractions": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.features": "9.0.4", "org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.system.io.pipelines": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.aspnetcore.http.connections.client": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.aspnetcore.http.connections.common": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.net.serversentevents": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.aspnetcore.http.connections.common": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.microsoft.aspnetcore.connections.abstractions": "9.0.4", "org.nuget.system.text.json": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.aspnetcore.signalr.client": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.aspnetcore.signalr.client.core": "9.0.4", "org.nuget.microsoft.aspnetcore.http.connections.client": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.aspnetcore.signalr.client.core": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.aspnetcore.signalr.protocols.json": "9.0.4", "org.nuget.microsoft.aspnetcore.signalr.common": "9.0.4", "org.nuget.microsoft.bcl.timeprovider": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4", "org.nuget.system.threading.channels": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.aspnetcore.signalr.common": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.aspnetcore.connections.abstractions": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.text.json": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.aspnetcore.signalr.protocols.json": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.aspnetcore.signalr.common": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.bcl.asyncinterfaces": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.bcl.memory": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.bcl.timeprovider": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.codeanalysis.analyzers": {"version": "3.11.0", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.codeanalysis.common": {"version": "4.13.0", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.codeanalysis.analyzers": "3.11.0", "org.nuget.system.collections.immutable": "8.0.0", "org.nuget.system.memory": "4.5.5", "org.nuget.system.reflection.metadata": "8.0.0", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0", "org.nuget.system.text.encoding.codepages": "7.0.0", "org.nuget.system.threading.tasks.extensions": "4.5.4", "org.nuget.system.buffers": "4.5.1", "org.nuget.system.numerics.vectors": "4.5.0"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.codeanalysis.csharp": {"version": "4.13.0", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.codeanalysis.common": "4.13.0", "org.nuget.microsoft.codeanalysis.analyzers": "3.11.0", "org.nuget.system.buffers": "4.5.1", "org.nuget.system.collections.immutable": "8.0.0", "org.nuget.system.memory": "4.5.5", "org.nuget.system.numerics.vectors": "4.5.0", "org.nuget.system.reflection.metadata": "8.0.0", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0", "org.nuget.system.text.encoding.codepages": "7.0.0", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.caching.abstractions": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.primitives": "9.0.4", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.primitives": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.abstractions": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.primitives": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.binder": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.commandline": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.environmentvariables": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.fileextensions": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.abstractions": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.physical": "9.0.4", "org.nuget.microsoft.extensions.primitives": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.json": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration.fileextensions": "9.0.4", "org.nuget.microsoft.extensions.configuration": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.abstractions": "9.0.4", "org.nuget.system.text.json": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.configuration.usersecrets": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration.json": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.abstractions": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.physical": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.dependencyinjection": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.dependencyinjection.abstractions": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.diagnostics": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration": "9.0.4", "org.nuget.microsoft.extensions.diagnostics.abstractions": "9.0.4", "org.nuget.microsoft.extensions.options.configurationextensions": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.diagnostics.abstractions": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.diagnostics.diagnosticsource": "9.0.4", "org.nuget.system.buffers": "4.5.1", "org.nuget.system.memory": "4.5.5"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.features": {"version": "9.0.4", "depth": 4, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.fileproviders.abstractions": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.primitives": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.fileproviders.physical": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.fileproviders.abstractions": "9.0.4", "org.nuget.microsoft.extensions.filesystemglobbing": "9.0.4", "org.nuget.microsoft.extensions.primitives": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.filesystemglobbing": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.hosting": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration.binder": "9.0.4", "org.nuget.microsoft.extensions.configuration.commandline": "9.0.4", "org.nuget.microsoft.extensions.configuration.environmentvariables": "9.0.4", "org.nuget.microsoft.extensions.configuration.fileextensions": "9.0.4", "org.nuget.microsoft.extensions.configuration.json": "9.0.4", "org.nuget.microsoft.extensions.configuration.usersecrets": "9.0.4", "org.nuget.microsoft.extensions.configuration": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection": "9.0.4", "org.nuget.microsoft.extensions.diagnostics": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.abstractions": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.physical": "9.0.4", "org.nuget.microsoft.extensions.hosting.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.configuration": "9.0.4", "org.nuget.microsoft.extensions.logging.console": "9.0.4", "org.nuget.microsoft.extensions.logging.debug": "9.0.4", "org.nuget.microsoft.extensions.logging.eventlog": "9.0.4", "org.nuget.microsoft.extensions.logging.eventsource": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.hosting.abstractions": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.diagnostics.abstractions": "9.0.4", "org.nuget.microsoft.extensions.fileproviders.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.diagnostics.diagnosticsource": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging.abstractions": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.system.diagnostics.diagnosticsource": "9.0.4", "org.nuget.system.buffers": "4.5.1", "org.nuget.system.memory": "4.5.5"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging.configuration": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration.binder": "9.0.4", "org.nuget.microsoft.extensions.configuration": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4", "org.nuget.microsoft.extensions.options.configurationextensions": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging.console": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.configuration": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.text.json": "9.0.4", "org.nuget.system.buffers": "4.5.1"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging.debug": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging.eventlog": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.system.diagnostics.eventlog": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.logging.eventsource": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging.abstractions": "9.0.4", "org.nuget.microsoft.extensions.logging": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.microsoft.extensions.primitives": "9.0.4", "org.nuget.system.text.json": "9.0.4", "org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.options": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.primitives": "9.0.4", "org.nuget.system.componentmodel.annotations": "5.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.options.configurationextensions": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.microsoft.extensions.configuration.abstractions": "9.0.4", "org.nuget.microsoft.extensions.configuration.binder": "9.0.4", "org.nuget.microsoft.extensions.dependencyinjection.abstractions": "9.0.4", "org.nuget.microsoft.extensions.options": "9.0.4", "org.nuget.microsoft.extensions.primitives": "9.0.4"}, "url": "https://package.openupm.com"}, "org.nuget.microsoft.extensions.primitives": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.r3": {"version": "1.3.0", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.timeprovider": "8.0.0", "org.nuget.system.buffers": "4.5.1", "org.nuget.system.componentmodel.annotations": "5.0.0", "org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0", "org.nuget.system.threading.channels": "8.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.system.buffers": {"version": "4.5.1", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.system.collections.immutable": {"version": "8.0.0", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.system.componentmodel.annotations": {"version": "5.0.0", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.system.diagnostics.diagnosticsource": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.system.diagnostics.eventlog": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.system.security.principal.windows": "5.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.system.io.pipelines": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.buffers": "4.5.1", "org.nuget.system.memory": "4.5.5", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.system.memory": {"version": "4.5.5", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.buffers": "4.5.1", "org.nuget.system.numerics.vectors": "4.4.0", "org.nuget.system.runtime.compilerservices.unsafe": "4.5.3"}, "url": "https://package.openupm.com"}, "org.nuget.system.net.serversentevents": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.system.memory": "4.5.5", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.system.numerics.vectors": {"version": "4.5.0", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.system.reflection.metadata": {"version": "8.0.0", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.collections.immutable": "8.0.0", "org.nuget.system.memory": "4.5.5"}, "url": "https://package.openupm.com"}, "org.nuget.system.runtime.compilerservices.unsafe": {"version": "6.0.0", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.system.security.principal.windows": {"version": "5.0.0", "depth": 4, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "org.nuget.system.text.encoding.codepages": {"version": "7.0.0", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.system.text.encodings.web": {"version": "9.0.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.buffers": "4.5.1", "org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0"}, "url": "https://package.openupm.com"}, "org.nuget.system.text.json": {"version": "9.0.4", "depth": 1, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.system.io.pipelines": "9.0.4", "org.nuget.system.text.encodings.web": "9.0.4", "org.nuget.system.buffers": "4.5.1", "org.nuget.system.memory": "4.5.5", "org.nuget.system.runtime.compilerservices.unsafe": "6.0.0", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.system.threading.channels": {"version": "9.0.4", "depth": 3, "source": "registry", "dependencies": {"org.nuget.microsoft.bcl.asyncinterfaces": "9.0.4", "org.nuget.system.threading.tasks.extensions": "4.5.4"}, "url": "https://package.openupm.com"}, "org.nuget.system.threading.tasks.extensions": {"version": "4.5.4", "depth": 2, "source": "registry", "dependencies": {"org.nuget.system.runtime.compilerservices.unsafe": "4.5.3"}, "url": "https://package.openupm.com"}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.cloth": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.subsystems": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.vehicles": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0"}}, "com.unity.modules.video": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.vr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.xr": "1.0.0"}}, "com.unity.modules.wind": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.xr": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.subsystems": "1.0.0"}}}}