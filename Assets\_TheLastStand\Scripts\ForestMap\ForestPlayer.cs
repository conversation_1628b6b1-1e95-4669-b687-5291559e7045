using Mirror;
using Steamworks;
using System;
using UnityEngine;
using UnityEngine.AI;

public class ForestPlayer : NetworkBehaviour
{
    [SerializeField] private Camera playerCamera;
    [SerializeField] private GameObject playerModel;
    [SerializeField] private Animator playerAnimator;
    [SerializeField] private CharacterController characterController;
    [SerializeField] private Transform camHolder;
    [SerializeField] private float lookSpeed = 2.0f;
    [SerializeField] private float lookXLimit = 45.0f;
    [SerializeField] private float walkingSpeed = 7.5f;
    [SerializeField] private float runningSpeed = 11.5f;
    [SerializeField] private float jumpHeight = 8.0f;
    [SerializeField] private float gravity = 20.0f;
    [SerializeField] private LayerMask groundMask;
    [SerializeField] private Transform groundedCheck;
    [SerializeField] private float groundedDistance = 2;

    private Vector3 velocity;
    private float rotationX = 0;
    private Vector2 inputDir;

    [Header("AI Navigation")]
    private NavMeshAgent navMeshAgent;
    [SyncVar(hook = nameof(OnAiControlledChanged))]
    public bool isAiControlled = false;
    private bool hasReportedArrival = false;

    // Field to store the original prefab camera if needed for toggling
    private Camera originalPlayerCamera;

    public override void OnStartAuthority()
    {
        base.OnStartAuthority();
        // If playerCamera is null (meaning a scene camera is expected or not yet assigned) 
        // or if it's already been replaced by an assigned scene camera, don't reactivate it here.
        // The ForestPlayerManager will handle scene camera assignment.
        // If no manager assigns a camera, and playerCamera was set in prefab, it should ideally be active by default or activated here.
        if (playerCamera != null && playerCamera.gameObject.activeSelf == false)
        {
             // This implies it's the prefab's own camera and hasn't been replaced yet.
            // playerCamera.gameObject.SetActive(true); // Let manager control this or ensure it's active if no manager overrides
        }

        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    void Awake()
    {
        navMeshAgent = GetComponent<NavMeshAgent>();
        if (navMeshAgent == null)
        {
            Debug.LogError("NavMeshAgent component not found on ForestPlayer! Please add one.", this);
        }
        if (characterController != null) characterController.enabled = true;
        if (navMeshAgent != null) navMeshAgent.enabled = false;

        // Store the original camera if one is assigned in the prefab
        if (playerCamera != null) {
            originalPlayerCamera = playerCamera;
        }
    }

    void Start()
    {
        if (!isOwned)
        {
            if (playerCamera != null) playerCamera.gameObject.SetActive(false);
        }
        OnAiControlledChanged(isAiControlled, isAiControlled);
    }

    void Update()
    {
        if (!isOwned)
        {
            return;
        }

        if (isAiControlled)
        {
            if (navMeshAgent != null && navMeshAgent.enabled && !hasReportedArrival)
            {
                if (!navMeshAgent.pathPending && navMeshAgent.remainingDistance <= navMeshAgent.stoppingDistance)
                {
                    if (navMeshAgent.remainingDistance == 0 || navMeshAgent.velocity.sqrMagnitude == 0f)
                    {
                        Debug.Log($"Player {netId} reached AI destination.");
                        CmdReachedExitWaypoint();
                        hasReportedArrival = true;
                    }
                }
            }
            if (playerAnimator != null && navMeshAgent != null && navMeshAgent.enabled)
            {
                float speed = navMeshAgent.velocity.magnitude / navMeshAgent.speed;
                playerAnimator.SetFloat("ForwardSpeed", speed);
            }
        }
        else
        {
            ProcessPlayerMovement();
            ProcessPlayerLook();
        }
    }

    void OnAiControlledChanged(bool oldState, bool newState)
    {
        if (characterController == null || navMeshAgent == null)
        {
            Debug.LogError("CharacterController or NavMeshAgent is null in OnAiControlledChanged. Ensure they are assigned.", this);
            if (navMeshAgent == null) navMeshAgent = GetComponent<NavMeshAgent>();
            if (characterController == null) characterController = GetComponent<CharacterController>();
            if (characterController == null || navMeshAgent == null) return;
        }

        if (newState == true)
        {
            characterController.enabled = false;
            navMeshAgent.enabled = true;
            if (isOwned) navMeshAgent.Warp(transform.position);

            Debug.Log($"Player {netId} AI Control ENABLED. NavAgent: {navMeshAgent.enabled}, CharCtrl: {characterController.enabled}");

        }
        else
        {
            if (navMeshAgent.enabled)
            {
                transform.position = navMeshAgent.transform.position;
            }
            navMeshAgent.enabled = false;
            characterController.enabled = true;
            hasReportedArrival = false;

            Debug.Log($"Player {netId} AI Control DISABLED. NavAgent: {navMeshAgent.enabled}, CharCtrl: {characterController.enabled}");

            if (isOwned)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }
            if (playerAnimator != null)
            {
                playerAnimator.SetFloat("ForwardSpeed", 0);
            }
        }
    }

    [TargetRpc]
    public void TargetRpc_StartAiExit(NetworkConnection target, Vector3 exitPosition)
    {
        if (navMeshAgent == null) {
            Debug.LogError($"NavMeshAgent is null on {gameObject.name} during TargetRpc_StartAiExit. Attempting to get component.");
            navMeshAgent = GetComponent<NavMeshAgent>();
            if (navMeshAgent == null) {
                Debug.LogError($"Failed to get NavMeshAgent on {gameObject.name}. AI cannot start.");
                return;
            }
        }

        if (!isAiControlled) {
            Debug.LogWarning($"TargetRpc_StartAiExit called on {gameObject.name}, but isAiControlled is false. Server should set this first.");
        }
        
        if (navMeshAgent.enabled)
        {
            navMeshAgent.SetDestination(exitPosition);
            hasReportedArrival = false;
            Debug.Log($"NavMeshAgent destination set to {exitPosition} for {gameObject.name}. Agent enabled: {navMeshAgent.enabled}");
        } else {
            Debug.LogError($"TargetRpc_StartAiExit: NavMeshAgent for {gameObject.name} is NOT enabled. Destination not set. isAiControlled: {isAiControlled}");
            if(isAiControlled) {
                navMeshAgent.enabled = true;
                if(navMeshAgent.enabled) {
                    navMeshAgent.SetDestination(exitPosition);
                    hasReportedArrival = false;
                    Debug.Log($"NavMeshAgent for {gameObject.name} enabled and destination set after retry.");
                } else {
                     Debug.LogError($"Still failed to enable NavMeshAgent for {gameObject.name}.");
                }
            }
        }
    }

    [TargetRpc]
    public void TargetRpc_EndAiExit(NetworkConnection target)
    {
        Debug.Log($"TargetRpc_EndAiExit called for {gameObject.name}. Control should be returned via isAiControlled SyncVar.");
    }

    [Command]
    private void CmdReachedExitWaypoint()
    {
        Debug.Log($"Player {netId} (CmdReachedExitWaypoint) reporting arrival to server.");
        HelicopterExitManager.Instance?.Server_PlayerReportedArrival(this);
    }

    [Command]
    public void CmdSetPlayerSlotId(int slotId)
    {
        PlayerIdentity identity = GetComponent<PlayerIdentity>();
        if (identity != null)
        {
            identity.PlayerSlotId = slotId;
            Debug.Log($"Player {netId} (CmdSetPlayerSlotId) requested to set Slot ID to {slotId}. Actual value on identity: {identity.PlayerSlotId}");
        }
        else
        {
            Debug.LogError($"Player {netId} (CmdSetPlayerSlotId) cannot set Slot ID - PlayerIdentity component missing!");
        }
    }

    private void ProcessPlayerMovement()
    {
        if (characterController == null || !characterController.enabled) return;

        bool isGrounded = Physics.CheckSphere(groundedCheck.position, groundedDistance, groundMask);
        if (isGrounded && velocity.y < 0)
        {
            velocity.y = -2f;
        }

        inputDir = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical"));
        Vector3 moveDir = new Vector3(inputDir.x, 0f, inputDir.y).normalized;
        
        Vector3 worldMoveDir = transform.TransformDirection(moveDir);

        float currentSpeed = Input.GetKey(KeyCode.LeftShift) ? runningSpeed : walkingSpeed;
        characterController.Move(worldMoveDir * currentSpeed * Time.deltaTime);

        if (Input.GetButtonDown("Jump") && isGrounded)
        {
            velocity.y = Mathf.Sqrt(jumpHeight * -2f * (Physics.gravity.y));
        }

        velocity.y += Physics.gravity.y * Time.deltaTime;
        characterController.Move(velocity * Time.deltaTime);

        if (playerAnimator != null)
        {
            float animationSpeed = inputDir.magnitude * (currentSpeed / walkingSpeed);
            playerAnimator.SetFloat("ForwardSpeed", animationSpeed);
        }
    }

    private void ProcessPlayerLook()
    {
        if (camHolder == null || playerCamera == null || !playerCamera.gameObject.activeInHierarchy) return;

        float mouseX = Input.GetAxis("Mouse X") * lookSpeed;
        float mouseY = Input.GetAxis("Mouse Y") * lookSpeed;

        rotationX -= mouseY;
        rotationX = Mathf.Clamp(rotationX, -lookXLimit, lookXLimit);

        camHolder.localRotation = Quaternion.Euler(rotationX, 0, 0);
        transform.Rotate(Vector3.up * mouseX);
    }

    // Method to be called by ForestPlayerManager to assign a specific scene camera
    public void AssignSceneCamera(Camera sceneCam)
    {
        if (!isOwned) return; // Only for the local player

        if (playerCamera != null && playerCamera != sceneCam) // If there's an existing, different camera
        {
            playerCamera.gameObject.SetActive(false);
        }

        playerCamera = sceneCam;
        
        if (playerCamera != null)
        {
            playerCamera.gameObject.SetActive(true);
            // Optional: Parent the scene camera to the camHolder for consistent look mechanics
            // This assumes camHolder is set up correctly for player look rotation.
            if (camHolder != null)
            {
                playerCamera.transform.SetParent(camHolder);
                playerCamera.transform.localPosition = Vector3.zero;
                playerCamera.transform.localRotation = Quaternion.identity;
            }
            Debug.Log($"Player {netId} assigned and activated scene camera: {sceneCam.name}");
        }
        else
        {
            Debug.LogWarning($"Player {netId} was assigned a null scene camera.");
            // Fallback to original camera if sceneCam is null and original exists?
            if (originalPlayerCamera != null)
            {
                playerCamera = originalPlayerCamera;
                playerCamera.gameObject.SetActive(true);
                if (camHolder != null && playerCamera.transform.parent != camHolder) // Reparent if needed
                {
                    playerCamera.transform.SetParent(camHolder);
                    playerCamera.transform.localPosition = Vector3.zero;
                    playerCamera.transform.localRotation = Quaternion.identity;
                }
            }
        }
    }
}